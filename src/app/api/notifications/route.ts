import { NextRequest, NextResponse } from "next/server";
import { withSubscriptionLimit } from "@/lib/api-subscription-middleware";
import { getNotifications } from "@/actions/notifications/notifications";

// GET /api/notifications - Get user's notifications (with subscription check)
async function getNotificationsHandler(request: NextRequest) {
  try {
    const userId = (request as any).userId; // Added by middleware
    
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const type = searchParams.get("type") || undefined;
    const isRead = searchParams.get("isRead");
    
    // Convert isRead to boolean if provided
    const isReadFilter = isRead === "true" ? true : isRead === "false" ? false : undefined;
    
    const result = await getNotifications({
      userId,
      page,
      limit,
      type: type as any,
      isRead: isReadFilter,
    });
    
    if (result.error) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }
    
    return NextResponse.json({
      success: true,
      notifications: result.notifications,
      pagination: result.pagination,
    });
  } catch (error) {
    console.error("Error getting notifications:", error);
    return NextResponse.json(
      { error: "Failed to get notifications" },
      { status: 500 }
    );
  }
}

// Export the wrapped handler with subscription limit check
export const GET = withSubscriptionLimit('notification')(getNotificationsHandler);
